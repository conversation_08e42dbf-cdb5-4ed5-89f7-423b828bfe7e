from flask import Flask, render_template_string, request, redirect, url_for, jsonify, send_file
import os
import json
from scraper.scraper import Scraper
from browser_automation.browser_scraper import BrowserScraper
from rss_generator.rss_generator import RSSGenerator
import datetime
import re
import logging
import time

SOURCES_FILE = os.path.join(os.path.dirname(__file__), '..', 'sources.json')
FEEDS_DIR = os.path.join(os.path.dirname(__file__), '..', 'feeds')
os.makedirs(FEEDS_DIR, exist_ok=True)

app = Flask(__name__)

# زر جلب الأخبار: مسار API (يجب أن يكون بعد تعريف app)
@app.route('/fetch_news', methods=['POST'])
def fetch_news():
    try:
        refresh_all()
        return jsonify({'ok': True})
    except Exception as e:
        return jsonify({'ok': False, 'error': str(e)})

# HTML templates
HOME_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>الرصد الإعلامي - الأخبار</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
</head>
<body class="bg-light">
<div class="container py-4">
    <h2 class="mb-4">جميع الأخبار المجمعة</h2>
    <a href="/admin" class="btn btn-primary mb-3">إدارة المصادر</a>
    <button id="fetchNewsBtn" class="btn btn-success mb-4 d-inline">جلب الأخبار</button>
    <form method="post" action="/refresh" class="mb-4 d-inline" style="display:none;">
        <button type="submit" class="btn btn-success">تحديث الأخبار الآن</button>
    </form>
    <span id="fetchNewsStatus" class="ms-2"></span>
    <form method="post" action="/delete_all_feeds" class="mb-4 d-inline" onsubmit="return confirm('هل أنت متأكد من حذف جميع الأخبار؟');">
        <button type="submit" class="btn btn-danger">مسح جميع الأخبار</button>
    </form>
    {% for site, items in all_news.items() %}
    <div class="card mb-4">
      <div class="card-header bg-info text-white"><b>{{site}}</b></div>
      <ul class="list-group list-group-flush">
      {% for item in items %}
        <li class="list-group-item">
          <a href="{{item['link']}}" target="_blank">{{item['title']}}</a>
          {% if item['date'] %}<span class="text-muted small">({{item['date']}})</span>{% endif %}
        </li>
      {% endfor %}
      </ul>
    </div>
    {% endfor %}
</div>
<script>
document.getElementById('fetchNewsBtn').onclick = function() {
    var btn = this;
    btn.disabled = true;
    btn.innerText = 'جاري جلب الأخبار...';
    document.getElementById('fetchNewsStatus').innerHTML = '';
    fetch('/fetch_news', {method:'POST'})
      .then(r=>r.json())
      .then(d=>{
        btn.disabled = false;
        btn.innerText = 'جلب الأخبار';
        if(d.ok){
          document.getElementById('fetchNewsStatus').innerHTML = '<span class="text-success">تم جلب الأخبار بنجاح، يتم التحديث...</span>';
          setTimeout(()=>{ location.reload(); }, 1200);
        }else{
          document.getElementById('fetchNewsStatus').innerHTML = '<span class="text-danger">فشل في جلب الأخبار</span>';
        }
      })
      .catch(()=>{
        btn.disabled = false;
        btn.innerText = 'جلب الأخبار';
        document.getElementById('fetchNewsStatus').innerHTML = '<span class="text-danger">حدث خطأ أثناء الجلب</span>';
      });
};
</script>
</body>
</html>
'''

ADMIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة المصادر الإخبارية</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
</head>
<body class="bg-light">
<div class="container py-4">
    <h2 class="mb-4">إدارة المصادر الإخبارية</h2>
    <a href="/" class="btn btn-secondary mb-3">عرض الأخبار</a>
    <form id="testAllForm" method="post" action="/admin/test_all" class="mb-3">
        <button type="submit" class="btn btn-warning">فحص جميع المصادر دفعة واحدة</button>
        <span id="testAllStatus"></span>
    </form>
    <form method="post" action="/admin/add" class="row g-2 mb-4">
        <div class="col-md-2"><input name="name" class="form-control" placeholder="اسم المصدر" required></div>
        <div class="col-md-3"><input name="url" class="form-control" placeholder="رابط الموقع" required></div>
        <div class="col-md-2"><input name="item" class="form-control" placeholder="CSS: العنصر الرئيسي"></div>
        <div class="col-md-1"><input name="title" class="form-control" placeholder="CSS: العنوان"></div>
        <div class="col-md-1"><input name="link" class="form-control" placeholder="CSS: الرابط"></div>
        <div class="col-md-1"><input name="date" class="form-control" placeholder="CSS: التاريخ (اختياري)"></div>
        <div class="col-md-1">
            <select name="protected" class="form-select">
                <option value="false">عادي</option>
                <option value="true">محمي</option>
            </select>
        </div>
        <div class="col-md-1"><button type="submit" class="btn btn-success w-100">إضافة</button></div>
    </form>
    <div class="table-responsive">
    <table class="table table-bordered table-striped align-middle">
        <thead class="table-light">
        <tr><th>الاسم</th><th>الرابط</th><th>العنصر</th><th>العنوان</th><th>الرابط</th><th>التاريخ</th><th>محمي؟</th><th>إجراءات</th></tr>
        </thead>
        <tbody>
        {% for s in sources %}
        <tr>
            <form method="post" action="/admin/edit/{{ loop.index0 }}">
            <td><input name="name" value="{{s.name}}" class="form-control" required></td>
            <td><input name="url" value="{{s.url}}" class="form-control" required></td>
            <td><input name="item" value="{{s.selectors.item|default('')}}" class="form-control"></td>
            <td><input name="title" value="{{s.selectors.title|default('')}}" class="form-control"></td>
            <td><input name="link" value="{{s.selectors.link|default('')}}" class="form-control"></td>
            <td><input name="date" value="{{s.selectors.date|default('')}}" class="form-control"></td>
            <td>
                <select name="protected" class="form-select">
                    <option value="false" {% if not s.protected %}selected{% endif %}>عادي</option>
                    <option value="true" {% if s.protected %}selected{% endif %}>محمي</option>
                </select>
            </td>
            <td class="actions">
                <button type="submit" class="btn btn-primary btn-sm">تعديل</button>
                <a href="/admin/delete/{{ loop.index0 }}" class="btn btn-danger btn-sm" onclick="return confirm('حذف المصدر؟')">حذف</a>
                <a href="#" class="btn btn-info btn-sm" onclick="testSource({{ loop.index0 }});return false;">اختبار</a>
                <span id="result{{ loop.index0 }}"></span>
            </td>
            </form>
        </tr>
        {% endfor %}
        </tbody>
    </table>
    </div>
    <script>
    document.getElementById('testAllForm').onsubmit = function(e) {
        e.preventDefault();
        let btn = this.querySelector('button');
        btn.disabled = true;
        btn.innerText = 'جاري الفحص...';
        fetch('/admin/test_all', {method:'POST'})
          .then(r=>r.json())
          .then(d=>{
            btn.disabled = false;
            btn.innerText = 'فحص جميع المصادر دفعة واحدة';
            for(let i in d.results){
              let el = document.getElementById('result'+i);
              if(el) el.innerHTML = d.results[i];
            }
            document.getElementById('testAllStatus').innerHTML = '<span class="text-success">تم الفحص، يتم تحديث الصفحة...'</span>;
            setTimeout(()=>{ location.reload(); }, 1200);
          })
          .catch(()=>{
            btn.disabled = false;
            btn.innerText = 'فحص جميع المصادر دفعة واحدة';
            document.getElementById('testAllStatus').innerHTML = '<span class="text-danger">حدث خطأ أثناء الفحص</span>';
          });
    };
    function testSource(idx) {
        fetch('/admin/test/' + idx).then(r=>r.json()).then(d=>{
            let el = document.getElementById('result'+idx);
            if(d.ok) el.innerHTML = '<span class="text-success">✔</span>';
            else el.innerHTML = '<span class="text-danger">✖ '+(d.error||'فشل')+'</span>';
        });
    }
    </script>
</div>
</body>
</html>
'''

def load_sources():
    if not os.path.exists(SOURCES_FILE):
        return []
    with open(SOURCES_FILE, encoding='utf-8') as f:
        return json.load(f)

def save_sources(sources):
    with open(SOURCES_FILE, 'w', encoding='utf-8') as f:
        json.dump(sources, f, ensure_ascii=False, indent=2)

def get_news_from_feed(site):
    import feedparser
    feed_path = os.path.join(FEEDS_DIR, f"{site['name']}.xml")
    if not os.path.exists(feed_path):
        return []
    d = feedparser.parse(feed_path)
    items = []
    for entry in d.entries:
        items.append({
            'title': entry.get('title', ''),
            'link': entry.get('link', ''),
            'date': entry.get('published', '')
        })
    # تصفية الأخبار حسب الشروط
    return filter_news(items)

def refresh_all():
    """تحديث جميع المصادر الإخبارية"""
    import concurrent.futures
    import time

    sources = load_sources()
    print(f"بدء تحديث {len(sources)} مصدر إخباري...")

    successful_sources = 0
    failed_sources = 0
    total_news = 0

    def process_site(site):
        nonlocal successful_sources, failed_sources, total_news

        site_name = site['name']
        print(f"🔄 بدء جلب: {site_name}")

        try:
            start_time = time.time()

            # اختيار نوع الجامع حسب نوع الموقع
            if site.get('protected'):
                scraper = BrowserScraper(site['url'], site.get('selectors', {}))
            else:
                scraper = Scraper(site['url'], site.get('selectors', {}))

            # جلب الأخبار
            items = scraper.scrape()

            if not items:
                print(f"⚠️  لم يتم العثور على أخبار في: {site_name}")
                failed_sources += 1
                return

            # طباعة عينة من الأخبار قبل الفلترة
            print(f"📰 تم جلب {len(items)} عنصر من {site_name}")
            if items:
                print(f"   عينة: {items[0].get('title', 'بدون عنوان')[:50]}...")

            # فلترة الأخبار
            filtered_items = filter_news(items)

            print(f"✅ {site_name}: {len(items)} → {len(filtered_items)} بعد الفلترة")

            # إنشاء RSS حتى لو كانت الأخبار قليلة
            rss = RSSGenerator(
                title=site_name,
                link=site['url'],
                description=f"خلاصة أخبار {site_name}"
            )

            # إضافة الأخبار المفلترة
            if filtered_items:
                rss.add_items(filtered_items)
                total_news += len(filtered_items)

            # كتابة ملف RSS
            rss_path = os.path.join(FEEDS_DIR, f"{site_name}.xml")
            rss.write(rss_path)

            elapsed_time = time.time() - start_time
            print(f"✅ تم الانتهاء من {site_name} في {elapsed_time:.1f} ثانية")
            successful_sources += 1

            # تأخير قصير لتجنب إرهاق الخوادم
            time.sleep(0.5)

        except Exception as e:
            print(f"❌ فشل تحديث {site_name}: {str(e)[:100]}")
            failed_sources += 1

            # إنشاء RSS فارغ في حالة الفشل
            try:
                rss = RSSGenerator(
                    title=site_name,
                    link=site.get('url', ''),
                    description=f"خلاصة أخبار {site_name} - فشل في التحديث"
                )
                rss_path = os.path.join(FEEDS_DIR, f"{site_name}.xml")
                rss.write(rss_path)
            except:
                pass

    # تشغيل المعالجة بشكل متوازي مع عدد محدود من العمليات
    max_workers = min(6, len(sources))  # تقليل عدد العمليات المتوازية

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # تقسيم المصادر إلى مجموعات لتجنب إرهاق الشبكة
        batch_size = 10
        for i in range(0, len(sources), batch_size):
            batch = sources[i:i + batch_size]
            futures = [executor.submit(process_site, site) for site in batch]

            # انتظار انتهاء المجموعة الحالية
            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    print(f"خطأ في معالجة المجموعة: {e}")

            # تأخير بين المجموعات
            if i + batch_size < len(sources):
                print(f"⏳ انتظار قبل المجموعة التالية...")
                time.sleep(2)

    print(f"\n📊 تقرير التحديث:")
    print(f"   ✅ نجح: {successful_sources}")
    print(f"   ❌ فشل: {failed_sources}")
    print(f"   📰 إجمالي الأخبار: {total_news}")
    print(f"   📁 ملفات RSS: {len(os.listdir(FEEDS_DIR))}")

    return {
        'successful': successful_sources,
        'failed': failed_sources,
        'total_news': total_news
    }

def filter_news(items):
    """فلترة الأخبار حسب التاريخ والمحتوى"""
    import datetime
    import unicodedata

    today = datetime.datetime.now().date()
    yesterday = today - datetime.timedelta(days=1)

    # كلمات مستبعدة
    exclude_keywords = [
        'كردستان', 'اربيل', 'دهوك', 'السليمانية', 'كركوك',
        'رياضة', 'رياضي', 'رياضية', 'كرة', 'مباراة', 'بطولة',
        'فنية', 'فن', 'فنان', 'مسرح', 'سينما', 'موسيقى',
        'طقس', 'حالة الطقس', 'جوي', 'جوية', 'الطقس', 'أمطار', 'حرارة',
        'إعلان', 'دعاية', 'تسوق', 'عروض', 'خصم'
    ]

    def normalize_ar_numbers(s):
        """تحويل الأرقام العربية إلى إنجليزية"""
        ar_digits = '٠١٢٣٤٥٦٧٨٩'
        en_digits = '0123456789'
        trans = str.maketrans(ar_digits, en_digits)
        return s.translate(trans)

    def is_recent_date(date_str):
        """فحص ما إذا كان التاريخ حديثاً (اليوم أو أمس)"""
        if not date_str:
            return True  # إذا لم يكن هناك تاريخ، نقبل الخبر

        # تنظيف النص
        s = normalize_ar_numbers(date_str)
        s = unicodedata.normalize('NFKC', s)

        # إزالة الكلمات الإضافية
        cleanup_words = ['نُشرت في', 'نشر في', 'نُشر في', 'Published on', 'منذ', 'قبل']
        for word in cleanup_words:
            s = s.replace(word, '')

        s = s.replace('،', '').replace('|', ' ').replace('-', ' ').replace('ـ', ' ')
        s = s.replace('ص', 'AM').replace('م', 'PM')

        # إزالة أسماء الأيام
        days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']
        for day in days:
            s = s.replace(day, '')

        # تحويل الأشهر العربية إلى إنجليزية
        months_map = {
            'يناير': 'January', 'فبراير': 'February', 'مارس': 'March',
            'ابريل': 'April', 'أبريل': 'April', 'مايو': 'May', 'يونيو': 'June',
            'يوليو': 'July', 'أغسطس': 'August', 'اغسطس': 'August',
            'سبتمبر': 'September', 'اكتوبر': 'October', 'أكتوبر': 'October',
            'نوفمبر': 'November', 'ديسمبر': 'December'
        }

        for ar_month, en_month in months_map.items():
            s = s.replace(ar_month, en_month)

        # تنظيف الرموز
        s = re.sub(r'[^\w\s:/-]', '', s)
        s = re.sub(r'\s+', ' ', s).strip()

        # أنماط التاريخ
        date_patterns = [
            r'(\d{4})[\-/](\d{1,2})[\-/](\d{1,2})',  # 2025-06-15
            r'(\d{1,2})[\-/](\d{1,2})[\-/](\d{4})',  # 15-06-2025
            r'(\d{1,2})\s+([A-Za-z]+)\s+(\d{4})',    # 15 June 2025
            r'منذ\s+(\d+)\s+(ساعة|ساعات|دقيقة|دقائق)',  # منذ 3 ساعات
            r'قبل\s+(\d+)\s+(ساعة|ساعات|دقيقة|دقائق)',   # قبل 2 ساعات
        ]

        for pattern in date_patterns:
            match = re.search(pattern, s)
            if match:
                try:
                    if 'منذ' in pattern or 'قبل' in pattern:
                        # إذا كان التاريخ بصيغة "منذ X ساعات" فهو حديث
                        return True
                    elif len(match.groups()) == 3:
                        if pattern == date_patterns[0]:  # YYYY-MM-DD
                            date_obj = datetime.date(int(match.group(1)), int(match.group(2)), int(match.group(3)))
                        elif pattern == date_patterns[1]:  # DD-MM-YYYY
                            date_obj = datetime.date(int(match.group(3)), int(match.group(2)), int(match.group(1)))
                        elif pattern == date_patterns[2]:  # DD Month YYYY
                            date_obj = datetime.datetime.strptime(f"{match.group(1)} {match.group(2)} {match.group(3)}", "%d %B %Y").date()

                        # قبول أخبار اليوم والأمس
                        return date_obj >= yesterday
                except Exception:
                    continue

        # إذا لم نتمكن من تحليل التاريخ، نقبل الخبر
        return True

    def is_valid_news(item):
        """فحص صحة الخبر"""
        title = (item.get('title') or '').strip()
        link = (item.get('link') or '').strip()

        if not title or not link:
            return False

        # فحص طول العنوان
        if len(title) < 10 or len(title) > 300:
            return False

        # فحص الكلمات المستبعدة
        title_lower = title.lower()
        for keyword in exclude_keywords:
            if keyword in title_lower:
                return False

        # فحص الرابط
        if not link.startswith('http'):
            return False

        return True

    filtered = []
    seen_titles = set()

    for item in items:
        if not is_valid_news(item):
            continue

        # تجنب التكرار
        title_normalized = re.sub(r'\s+', ' ', item['title'].strip().lower())
        if title_normalized in seen_titles:
            continue

        # فحص التاريخ
        if not is_recent_date(item.get('date')):
            continue

        seen_titles.add(title_normalized)
        filtered.append(item)

    return filtered

@app.route('/', methods=['GET'])
def home():
    sources = load_sources()
    all_news = {s['name']: get_news_from_feed(s) for s in sources}
    return render_template_string(HOME_TEMPLATE, all_news=all_news)

@app.route('/refresh', methods=['POST'])
def refresh():
    refresh_all()
    return redirect(url_for('home'))

@app.route('/admin', methods=['GET'])
def admin():
    sources = load_sources()
    return render_template_string(ADMIN_TEMPLATE, sources=sources)

@app.route('/admin/add', methods=['POST'])
def add_source():
    sources = load_sources()
    selectors = {}
    if request.form.get('item'): selectors['item'] = request.form['item']
    if request.form.get('title'): selectors['title'] = request.form['title']
    if request.form.get('link'): selectors['link'] = request.form['link']
    if request.form.get('date'): selectors['date'] = request.form['date']
    sources.append({
        'name': request.form['name'],
        'url': request.form['url'],
        'selectors': selectors,
        'protected': request.form['protected'] == 'true'
    })
    save_sources(sources)
    return redirect(url_for('admin'))

@app.route('/admin/edit/<int:idx>', methods=['POST'])
def edit_source(idx):
    sources = load_sources()
    if 0 <= idx < len(sources):
        selectors = {}
        if request.form.get('item'): selectors['item'] = request.form['item']
        if request.form.get('title'): selectors['title'] = request.form['title']
        if request.form.get('link'): selectors['link'] = request.form['link']
        if request.form.get('date'): selectors['date'] = request.form['date']
        sources[idx] = {
            'name': request.form['name'],
            'url': request.form['url'],
            'selectors': selectors,
            'protected': request.form['protected'] == 'true'
        }
        save_sources(sources)
    return redirect(url_for('admin'))

@app.route('/admin/delete/<int:idx>')
def delete_source(idx):
    sources = load_sources()
    if 0 <= idx < len(sources):
        sources.pop(idx)
        save_sources(sources)
    return redirect(url_for('admin'))

@app.route('/admin/test/<int:idx>')
def test_source(idx):
    sources = load_sources()
    if 0 <= idx < len(sources):
        src = sources[idx]
        try:
            if src.get('protected'):
                items = BrowserScraper(src['url'], src.get('selectors', {})).scrape()
            else:
                items = Scraper(src['url'], src.get('selectors', {})).scrape()
            if items:
                return jsonify({'ok': True, 'count': len(items)})
            else:
                return jsonify({'ok': False, 'error': 'لا توجد أخبار'})
        except Exception as e:
            return jsonify({'ok': False, 'error': str(e)})
    return jsonify({'ok': False, 'error': 'مصدر غير موجود'})

@app.route('/admin/test_all', methods=['POST'])
def test_all_sources():
    sources = load_sources()
    results = {}
    for idx, src in enumerate(sources):
        try:
            print(f"فحص المصدر: {src['name']}")
            if src.get('protected'):
                items = BrowserScraper(src['url'], src.get('selectors', {})).scrape()
            else:
                items = Scraper(src['url'], src.get('selectors', {})).scrape()
            if items:
                results[str(idx)] = '<span class="text-success">✔</span>'
            else:
                results[str(idx)] = '<span class="text-danger">✖ لا توجد أخبار</span>'
        except Exception as e:
            print(f"خطأ في {src['name']}: {e}")
            results[str(idx)] = f'<span class="text-danger">✖ خطأ</span>'
    print("نتائج الفحص الجماعي:", results)
    return jsonify({'results': results})

@app.route('/rss/<site>')
def get_rss(site):
    path = f'feeds/{site}.xml'
    if os.path.exists(path):
        return send_file(path, mimetype='application/rss+xml')
    return jsonify({'error': 'RSS not found'}), 404

@app.route('/delete_all_feeds', methods=['POST'])
def delete_all_feeds():
    for fname in os.listdir(FEEDS_DIR):
        if fname.endswith('.xml'):
            try:
                os.remove(os.path.join(FEEDS_DIR, fname))
            except Exception:
                pass
    return redirect(url_for('home'))

if __name__ == '__main__':
    app.run(debug=True, port=5001)
