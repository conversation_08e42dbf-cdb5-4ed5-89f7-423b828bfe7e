from bs4 import BeautifulSoup
import requests
import re
from urllib.parse import urljoin, urlparse
import time

class Scraper:
    def __init__(self, url, selectors):
        self.url = url
        self.selectors = selectors
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

    def fetch(self):
        try:
            resp = self.session.get(self.url, timeout=30)
            resp.raise_for_status()
            return resp.text
        except Exception as e:
            print(f"خطأ في جلب {self.url}: {e}")
            raise

    def extract_date_from_text(self, text):
        """استخراج التاريخ من النص"""
        if not text:
            return None

        # أنماط التاريخ المختلفة
        date_patterns = [
            r'(\d{4})-(\d{2})-(\d{2})',  # 2025-06-15
            r'(\d{2})/(\d{2})/(\d{4})',  # 15/06/2025
            r'(\d{2})-(\d{2})-(\d{4})',  # 15-06-2025
            r'(\d{1,2})\s+(يناير|فبراير|مارس|ابريل|أبريل|مايو|يونيو|يوليو|أغسطس|اغسطس|سبتمبر|اكتوبر|أكتوبر|نوفمبر|ديسمبر)\s+(\d{4})',
        ]

        for pattern in date_patterns:
            match = re.search(pattern, text)
            if match:
                return text.strip()

        return None

    def is_news_link(self, href, text):
        """تحديد ما إذا كان الرابط خبراً أم لا"""
        if not href or not text:
            return False

        # تجاهل الروابط غير المفيدة
        ignore_patterns = [
            r'#', r'javascript:', r'mailto:', r'tel:',
            r'/category/', r'/tag/', r'/author/',
            r'facebook\.com', r'twitter\.com', r'instagram\.com',
            r'youtube\.com', r'whatsapp\.com', r'telegram\.me',
            r'\.pdf$', r'\.jpg$', r'\.png$', r'\.gif$',
            r'/search', r'/login', r'/register', r'/contact',
            r'رياضة', r'رياضي', r'طقس', r'فن', r'فنية'
        ]

        for pattern in ignore_patterns:
            if re.search(pattern, href.lower()) or re.search(pattern, text.lower()):
                return False

        # يجب أن يكون النص طويلاً بما فيه الكفاية ليكون عنوان خبر
        if len(text.strip()) < 15 or len(text.strip()) > 200:
            return False

        return True

    def parse(self, html):
        soup = BeautifulSoup(html, 'html.parser')
        items = []

        # إزالة العناصر غير المرغوب فيها
        for element in soup(['script', 'style', 'nav', 'footer', 'aside']):
            element.decompose()

        # البحث عن الأخبار باستخدام محددات مختلفة
        news_selectors = [
            'article a[href]',
            '.post a[href]', '.news a[href]', '.item a[href]',
            '.entry a[href]', '.content a[href]',
            'h1 a[href]', 'h2 a[href]', 'h3 a[href]',
            '.title a[href]', '.headline a[href]',
            '[class*="news"] a[href]', '[class*="post"] a[href]',
            '[class*="article"] a[href]', '[class*="story"] a[href]'
        ]

        found_links = set()

        for selector in news_selectors:
            try:
                links = soup.select(selector)
                for link in links:
                    href = link.get('href', '').strip()
                    text = link.get_text(strip=True)

                    if not href or href in found_links:
                        continue

                    # تحويل الروابط النسبية إلى مطلقة
                    if href.startswith('/'):
                        href = urljoin(self.url, href)
                    elif not href.startswith('http'):
                        continue

                    if self.is_news_link(href, text):
                        # البحث عن التاريخ في العنصر الأب أو الأشقاء
                        date_text = None
                        parent = link.parent
                        if parent:
                            # البحث في النص المحيط
                            parent_text = parent.get_text()
                            date_text = self.extract_date_from_text(parent_text)

                            # البحث في العناصر الأشقاء
                            if not date_text:
                                for sibling in parent.find_all(['time', 'span', 'div'], class_=re.compile(r'date|time')):
                                    date_text = self.extract_date_from_text(sibling.get_text())
                                    if date_text:
                                        break

                        items.append({
                            'title': text,
                            'link': href,
                            'date': date_text
                        })
                        found_links.add(href)

                        if len(items) >= 50:  # حد أقصى للأخبار لكل موقع
                            break
            except Exception as e:
                continue

        # إذا لم نجد أخبار كافية، نستخدم الطريقة القديمة كاحتياطي
        if len(items) < 5:
            for a in soup.find_all('a', href=True):
                href = a.get('href', '').strip()
                text = a.get_text(strip=True)

                if href in found_links:
                    continue

                if href.startswith('/'):
                    href = urljoin(self.url, href)
                elif not href.startswith('http'):
                    continue

                if self.is_news_link(href, text):
                    items.append({
                        'title': text,
                        'link': href,
                        'date': None
                    })
                    found_links.add(href)

                    if len(items) >= 30:
                        break

        return items

    def scrape(self):
        html = self.fetch()
        return self.parse(html)
