from bs4 import BeautifulSoup
import requests

class Scraper:
    def __init__(self, url, selectors):
        self.url = url
        self.selectors = selectors

    def fetch(self):
        resp = requests.get(self.url)
        resp.raise_for_status()
        return resp.text

    def parse(self, html):
        soup = BeautifulSoup(html, 'html.parser')
        items = []
        # جلب جميع الروابط والعناوين من الصفحة بالكامل
        for a in soup.find_all('a', href=True):
            text = a.get_text(strip=True)
            href = a['href']
            if text and href and len(text) > 10:
                items.append({'title': text, 'link': href, 'date': None})
        return items

    def scrape(self):
        html = self.fetch()
        return self.parse(html)
