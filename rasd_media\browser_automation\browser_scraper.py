from playwright.sync_api import sync_playwright

class BrowserScraper:
    def __init__(self, url, selectors):
        self.url = url
        self.selectors = selectors

    def scrape(self):
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            context = browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                locale="ar-IQ",
                viewport={"width": 1280, "height": 800},
                java_script_enabled=True,
            )
            page = context.new_page()
            page.set_extra_http_headers({
                "Accept-Language": "ar,en-US;q=0.9,en;q=0.8",
                "Referer": self.url,
                "DNT": "1",
                "Upgrade-Insecure-Requests": "1",
            })
            # إعادة المحاولة حتى 3 مرات مع زيادة المهلة
            last_exc = None
            for attempt in range(3):
                try:
                    page.goto(self.url, wait_until="domcontentloaded", timeout=60000)
                    break
                except Exception as e:
                    last_exc = e
                    if attempt == 2:
                        browser.close()
                        raise e
            # انتظر ظهور أي رابط في الصفحة
            try:
                page.wait_for_selector('a[href]', timeout=10000)
            except Exception:
                pass
            html = page.content()
            browser.close()
        from scraper.scraper import Scraper
        # تجاهل المحددات، جلب كل الروابط والعناوين
        return Scraper(self.url, {}).parse(html)
