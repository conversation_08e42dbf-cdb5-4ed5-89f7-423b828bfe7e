from playwright.sync_api import sync_playwright
import time
import random

class BrowserScraper:
    def __init__(self, url, selectors):
        self.url = url
        self.selectors = selectors

    def scrape(self):
        items = []
        try:
            with sync_playwright() as p:
                browser = p.chromium.launch(
                    headless=True,
                    args=[
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-accelerated-2d-canvas',
                        '--no-first-run',
                        '--no-zygote',
                        '--disable-gpu'
                    ]
                )
                context = browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    locale="ar-IQ",
                    viewport={"width": 1920, "height": 1080},
                    java_script_enabled=True,
                    ignore_https_errors=True,
                )

                page = context.new_page()
                page.set_extra_http_headers({
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                    "Accept-Language": "ar,en-US;q=0.9,en;q=0.8",
                    "Accept-Encoding": "gzip, deflate, br",
                    "DNT": "1",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "none",
                    "Cache-Control": "max-age=0"
                })

                # إعادة المحاولة حتى 3 مرات
                last_exc = None
                for attempt in range(3):
                    try:
                        print(f"محاولة {attempt + 1} لجلب: {self.url}")

                        # تأخير عشوائي لتجنب الحظر
                        if attempt > 0:
                            delay = random.uniform(2, 5)
                            time.sleep(delay)

                        page.goto(self.url, wait_until="domcontentloaded", timeout=45000)

                        # انتظار تحميل المحتوى
                        try:
                            page.wait_for_selector('a[href]', timeout=10000)
                        except:
                            pass

                        # انتظار إضافي للمحتوى الديناميكي
                        page.wait_for_timeout(3000)

                        # التمرير لأسفل لتحميل المحتوى الكسول
                        try:
                            page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                            page.wait_for_timeout(2000)
                        except:
                            pass

                        html = page.content()
                        break

                    except Exception as e:
                        last_exc = e
                        print(f"فشل في المحاولة {attempt + 1}: {e}")
                        if attempt == 2:
                            raise e

                browser.close()

                # استخدام المحلل المحسن
                from scraper.scraper import Scraper
                scraper = Scraper(self.url, self.selectors)
                items = scraper.parse(html)

                print(f"تم جلب {len(items)} خبر من {self.url}")
                return items

        except Exception as e:
            print(f"خطأ في Browser Scraper لـ {self.url}: {e}")
            return []
