from playwright.sync_api import sync_playwright
from bs4 import BeautifulSoup
import sys
import time

if len(sys.argv) > 1:
    url = sys.argv[1]
else:
    url = input("Enter URL: ")

with sync_playwright() as p:
    browser = p.chromium.launch(headless=True)
    page = browser.new_page()
    page.goto(url, wait_until="domcontentloaded", timeout=60000)
    # تنفيذ scroll للأسفل عدة مرات لمحاولة تحميل الأخبار الديناميكية
    for _ in range(10):
        page.mouse.wheel(0, 1000)
        time.sleep(1)
    page.wait_for_timeout(12000)  # انتظر 12 ثانية إضافية
    html = page.content()
    browser.close()

soup = BeautifulSoup(html, 'html.parser')

print("\n=== All links with text (length > 10) ===\n")
for a in soup.find_all('a', href=True):
    text = a.get_text(strip=True)
    href = a['href']
    if text and len(text) > 10:
        print(f"TEXT: {text}\nHREF: {href}\n---")
