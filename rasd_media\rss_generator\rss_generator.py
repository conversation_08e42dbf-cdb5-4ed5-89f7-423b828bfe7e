from feedgen.feed import FeedGenerator
from datetime import datetime, timezone
import re

class RSSGenerator:
    def __init__(self, title, link, description):
        self.fg = FeedGenerator()
        self.fg.title(title)
        self.fg.link(href=link, rel='alternate')
        self.fg.id(link)
        self.fg.description(description)
        self.fg.author({'name': 'نظام الرصد الإعلامي', 'email': '<EMAIL>'})
        self.fg.language('ar')
        self.fg.lastBuildDate(datetime.now(timezone.utc))
        self.fg.generator('نظام الرصد الإعلامي v2.0')

    def parse_date(self, date_str):
        """تحليل التاريخ من النص العربي"""
        if not date_str:
            return datetime.now(timezone.utc)

        try:
            # أنماط التاريخ المختلفة
            patterns = [
                r'(\d{4})-(\d{2})-(\d{2})',  # 2025-06-15
                r'(\d{2})/(\d{2})/(\d{4})',  # 15/06/2025
                r'(\d{2})-(\d{2})-(\d{4})',  # 15-06-2025
            ]

            for pattern in patterns:
                match = re.search(pattern, date_str)
                if match:
                    if pattern == patterns[0]:  # YYYY-MM-DD
                        year, month, day = int(match.group(1)), int(match.group(2)), int(match.group(3))
                    else:  # DD/MM/YYYY or DD-MM-YYYY
                        day, month, year = int(match.group(1)), int(match.group(2)), int(match.group(3))

                    return datetime(year, month, day, tzinfo=timezone.utc)

            # إذا فشل التحليل، استخدم التاريخ الحالي
            return datetime.now(timezone.utc)

        except Exception:
            return datetime.now(timezone.utc)

    def add_items(self, items):
        """إضافة الأخبار إلى RSS"""
        for i, item in enumerate(items):
            try:
                fe = self.fg.add_entry()

                # العنوان
                title = item.get('title', f'خبر رقم {i+1}').strip()
                fe.title(title)

                # الرابط
                link = item.get('link', '').strip()
                if link:
                    fe.link(href=link)
                    fe.id(link)
                else:
                    fe.id(f"item-{i}-{hash(title)}")

                # الوصف
                description = title  # استخدام العنوان كوصف
                if len(description) > 200:
                    description = description[:200] + "..."
                fe.description(description)

                # التاريخ
                pub_date = self.parse_date(item.get('date'))
                fe.pubDate(pub_date)

                # معلومات إضافية
                fe.author({'name': 'مصدر الخبر'})

            except Exception as e:
                print(f"خطأ في إضافة الخبر: {e}")
                continue

    def rss_str(self):
        """إرجاع RSS كنص"""
        return self.fg.rss_str(pretty=True)

    def write(self, filename):
        """كتابة RSS إلى ملف"""
        try:
            self.fg.rss_file(filename)
            print(f"تم حفظ RSS في: {filename}")
        except Exception as e:
            print(f"خطأ في حفظ RSS: {e}")
            raise
