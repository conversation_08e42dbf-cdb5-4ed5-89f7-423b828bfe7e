from feedgen.feed import FeedGenerator

class RSSGenerator:
    def __init__(self, title, link, description):
        self.fg = FeedGenerator()
        self.fg.title(title)
        self.fg.link(href=link)
        self.fg.description(description)

    def add_items(self, items):
        for item in items:
            fe = self.fg.add_entry()
            fe.title(item['title'])
            fe.link(href=item['link'])
            if item.get('date'):
                fe.pubDate(item['date'])

    def rss_str(self):
        return self.fg.rss_str(pretty=True)

    def write(self, filename):
        self.fg.rss_file(filename)
