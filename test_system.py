#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت اختبار نظام الرصد الإعلامي
"""

import os
import sys
import json
import time

# إعداد المسارات
project_root = os.path.dirname(__file__)
rasd_media_path = os.path.join(project_root, "rasd_media")

if not os.path.exists(rasd_media_path):
    print(f"❌ خطأ: لا يمكن العثور على مجلد rasd_media في {rasd_media_path}")
    sys.exit(1)

os.chdir(rasd_media_path)
sys.path.insert(0, os.path.abspath("."))

def test_imports():
    """اختبار استيراد الوحدات"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        from scraper.scraper import Scraper
        print("✅ تم استيراد Scraper")
    except ImportError as e:
        print(f"❌ فشل استيراد Scraper: {e}")
        return False
    
    try:
        from browser_automation.browser_scraper import BrowserScraper
        print("✅ تم استيراد BrowserScraper")
    except ImportError as e:
        print(f"❌ فشل استيراد BrowserScraper: {e}")
        return False
    
    try:
        from rss_generator.rss_generator import RSSGenerator
        print("✅ تم استيراد RSSGenerator")
    except ImportError as e:
        print(f"❌ فشل استيراد RSSGenerator: {e}")
        return False
    
    try:
        from api.app import app
        print("✅ تم استيراد Flask App")
    except ImportError as e:
        print(f"❌ فشل استيراد Flask App: {e}")
        return False
    
    return True

def test_scraper():
    """اختبار جامع الأخبار العادي"""
    print("\n🔍 اختبار جامع الأخبار العادي...")
    
    try:
        from scraper.scraper import Scraper
        
        # اختبار موقع بسيط
        test_url = "https://www.bbc.com/arabic"
        scraper = Scraper(test_url, {})
        
        print(f"📡 جلب الأخبار من: {test_url}")
        items = scraper.scrape()
        
        print(f"✅ تم جلب {len(items)} عنصر")
        
        if items:
            print("📰 عينة من الأخبار:")
            for i, item in enumerate(items[:3]):
                print(f"   {i+1}. {item.get('title', 'بدون عنوان')[:50]}...")
        
        return len(items) > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Scraper: {e}")
        return False

def test_rss_generator():
    """اختبار مولد RSS"""
    print("\n🔍 اختبار مولد RSS...")
    
    try:
        from rss_generator.rss_generator import RSSGenerator
        
        # إنشاء RSS تجريبي
        rss = RSSGenerator(
            title="اختبار RSS",
            link="https://test.com",
            description="اختبار مولد RSS"
        )
        
        # إضافة أخبار تجريبية
        test_items = [
            {
                'title': 'خبر تجريبي 1',
                'link': 'https://test.com/news1',
                'date': '2025-06-15'
            },
            {
                'title': 'خبر تجريبي 2',
                'link': 'https://test.com/news2',
                'date': '2025-06-15'
            }
        ]
        
        rss.add_items(test_items)
        
        # اختبار إنتاج RSS
        rss_content = rss.rss_str()
        
        if b'<rss' in rss_content and b'</rss>' in rss_content:
            print("✅ تم إنتاج RSS بنجاح")
            
            # حفظ ملف تجريبي
            test_file = "test_rss.xml"
            rss.write(test_file)
            
            if os.path.exists(test_file):
                print(f"✅ تم حفظ ملف RSS: {test_file}")
                os.remove(test_file)  # حذف الملف التجريبي
                return True
            else:
                print("❌ فشل في حفظ ملف RSS")
                return False
        else:
            print("❌ محتوى RSS غير صحيح")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار RSS Generator: {e}")
        return False

def test_sources_file():
    """اختبار ملف المصادر"""
    print("\n🔍 اختبار ملف المصادر...")
    
    sources_file = "sources.json"
    
    if not os.path.exists(sources_file):
        print(f"❌ ملف المصادر غير موجود: {sources_file}")
        return False
    
    try:
        with open(sources_file, 'r', encoding='utf-8') as f:
            sources = json.load(f)
        
        print(f"✅ تم تحميل {len(sources)} مصدر إخباري")
        
        # فحص بنية المصادر
        valid_sources = 0
        for source in sources:
            if 'name' in source and 'url' in source:
                valid_sources += 1
        
        print(f"✅ {valid_sources} مصدر صحيح من أصل {len(sources)}")
        
        return valid_sources > 0
        
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف المصادر: {e}")
        return False

def test_feeds_directory():
    """اختبار مجلد الخلاصات"""
    print("\n🔍 اختبار مجلد الخلاصات...")
    
    feeds_dir = "feeds"
    
    if not os.path.exists(feeds_dir):
        print(f"📁 إنشاء مجلد الخلاصات: {feeds_dir}")
        os.makedirs(feeds_dir, exist_ok=True)
    
    # عد ملفات RSS الموجودة
    rss_files = [f for f in os.listdir(feeds_dir) if f.endswith('.xml')]
    print(f"📁 يحتوي مجلد الخلاصات على {len(rss_files)} ملف RSS")
    
    return True

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء اختبار نظام الرصد الإعلامي")
    print("=" * 50)
    
    tests = [
        ("استيراد الوحدات", test_imports),
        ("ملف المصادر", test_sources_file),
        ("مجلد الخلاصات", test_feeds_directory),
        ("مولد RSS", test_rss_generator),
        ("جامع الأخبار", test_scraper),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "="*50)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للعمل")
        return True
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
