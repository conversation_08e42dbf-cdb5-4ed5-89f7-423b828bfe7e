import os
import json
from scraper.scraper import Scraper
from browser_automation.browser_scraper import Browser<PERSON>craper
from rss_generator.rss_generator import RSSGenerator
from scheduler.scheduler import Scheduler

SOURCES_FILE = os.path.join(os.path.dirname(__file__), 'sources.json')
FEEDS_DIR = os.path.join(os.path.dirname(__file__), 'feeds')
os.makedirs(FEEDS_DIR, exist_ok=True)

def load_sources():
    with open(SOURCES_FILE, encoding='utf-8') as f:
        return json.load(f)

def generate_feed(site):
    try:
        if site.get('protected'):
            items = BrowserScraper(site['url'], site.get('selectors', {})).scrape()
        else:
            items = Scraper(site['url'], site.get('selectors', {})).scrape()
        rss = RSSGenerator(site['name'], site['url'], f"خلاصة أخبار {site['name']}")
        rss.add_items(items)
        rss.write(os.path.join(FEEDS_DIR, f"{site['name']}.xml"))
        print(f"تم تحديث RSS لـ {site['name']}")
    except Exception as e:
        print(f"فشل تحديث {site['name']}: {e}")

def main():
    scheduler = Scheduler()
    sources = load_sources()
    for site in sources:
        scheduler.add_job(lambda s=site: generate_feed(s), interval_minutes=30)
    scheduler.run()

if __name__ == '__main__':
    main()
