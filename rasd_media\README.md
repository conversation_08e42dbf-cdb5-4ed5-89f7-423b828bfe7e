# الرصد الإعلامي

مشروع لجمع الأخبار من المواقع وتحويلها إلى خلاصات RSS تلقائيًا، مع دعم المواقع المحمية وتوفير API اختياري.

## المكونات:
- وحدة الزحف (Scraper)
- مولّد RSS (RSS Generator)
- وحدة تجاوز الحماية (Browser Automation)
- وحدة التحديث التلقائي (Scheduler)
- وحدة العرض أو API (اختياري)

## المتطلبات
- Python 3.10+
- requests
- beautifulsoup4
- playwright
- feedgen
- schedule
- flask

## التثبيت
```bash
pip install -r requirements.txt
playwright install
```

## التشغيل
```bash
python main.py
```
