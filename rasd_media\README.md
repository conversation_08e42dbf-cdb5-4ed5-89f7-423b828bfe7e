# نظام الرصد الإعلامي v2.0

نظام محسن لجمع وتجميع الأخبار من مصادر إعلامية عراقية متعددة وتحويلها إلى خلاصات RSS.

## ✨ المميزات الجديدة

- 🔍 **جمع ذكي للأخبار**: خوارزميات محسنة لاستخراج الأخبار من المواقع
- 🛡️ **دعم المواقع المحمية**: استخدام Playwright للمواقع التي تتطلب JavaScript
- 📅 **فلترة متقدمة**: فلترة الأخبار حسب التاريخ والمحتوى
- 🚀 **أداء محسن**: معالجة متوازية مع تحكم في الموارد
- 📊 **تقارير مفصلة**: إحصائيات عن عملية جمع الأخبار
- 🔧 **واجهة إدارية محسنة**: إدارة سهلة للمصادر الإخبارية

## 📋 المتطلبات

- Python 3.8 أو أحدث
- نظام تشغيل: Windows, Linux, macOS
- ذاكرة: 2GB RAM على الأقل
- مساحة تخزين: 500MB

## 🚀 التثبيت السريع

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd inews
```

### 2. تشغيل سكريبت الإعداد التلقائي
```bash
python setup.py
```

### 3. تشغيل النظام
```bash
python run.py
```

## 🔧 التثبيت اليدوي

### 1. تثبيت المتطلبات
```bash
cd rasd_media
pip install -r requirements.txt
```

### 2. إعداد Playwright
```bash
playwright install chromium
playwright install-deps
```

### 3. إنشاء المجلدات المطلوبة
```bash
mkdir -p feeds logs
```

## 🎯 الاستخدام

### تشغيل الخادم
```bash
python run.py
```

### الوصول للنظام
- **الصفحة الرئيسية**: http://localhost:5001
- **لوحة الإدارة**: http://localhost:5001/admin
- **خلاصات RSS**: http://localhost:5001/rss/[اسم_المصدر]

### جلب الأخبار
1. اذهب إلى الصفحة الرئيسية
2. اضغط على زر "جلب الأخبار"
3. انتظر انتهاء العملية
4. ستظهر الأخبار المحدثة

## 🛠️ إدارة المصادر

### إضافة مصدر جديد
1. اذهب إلى `/admin`
2. املأ النموذج بمعلومات المصدر:
   - **الاسم**: اسم المصدر الإخباري
   - **الرابط**: URL الموقع
   - **المحددات**: CSS selectors (اختياري)
   - **النوع**: عادي أو محمي

### تعديل مصدر موجود
1. في صفحة الإدارة، عدل البيانات في الجدول
2. اضغط "تعديل" لحفظ التغييرات

### اختبار المصادر
- **اختبار مصدر واحد**: اضغط "اختبار" بجانب المصدر
- **اختبار جميع المصادر**: اضغط "فحص جميع المصادر"

## 📊 نظام الفلترة

### فلترة التاريخ
- يعرض أخبار اليوم والأمس فقط
- يدعم تنسيقات تاريخ متعددة (عربي/إنجليزي)

### فلترة المحتوى
يستبعد الأخبار التي تحتوي على:
- أخبار كردستان (اربيل، دهوك، السليمانية)
- الأخبار الرياضية
- أخبار الطقس
- المحتوى الفني والترفيهي

## 🔍 استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في استيراد الوحدات
```bash
# تأكد من تثبيت المتطلبات
pip install -r rasd_media/requirements.txt
```

#### فشل في جلب الأخبار
```bash
# اختبر الاتصال بالإنترنت
# تحقق من حالة المواقع الإخبارية
# راجع ملف السجلات: rasd_media.log
```

#### مشاكل Playwright
```bash
# إعادة تثبيت المتصفحات
playwright install chromium --force
```

### تشغيل الاختبارات
```bash
python test_system.py
```

## 📁 هيكل المشروع

```
inews/
├── run.py                 # نقطة الدخول الرئيسية
├── setup.py              # سكريپت الإعداد
├── test_system.py        # اختبارات النظام
└── rasd_media/
    ├── api/
    │   ├── app.py         # تطبيق Flask
    │   └── admin.py       # واجهة الإدارة
    ├── scraper/
    │   └── scraper.py     # جامع الأخبار العادي
    ├── browser_automation/
    │   └── browser_scraper.py  # جامع الأخبار المحمي
    ├── rss_generator/
    │   └── rss_generator.py    # مولد RSS
    ├── scheduler/
    │   └── scheduler.py   # نظام الجدولة
    ├── feeds/             # مجلد خلاصات RSS
    ├── sources.json       # قاعدة بيانات المصادر
    └── requirements.txt   # المتطلبات
```

## 🔧 التخصيص

### إضافة محددات CSS مخصصة
```json
{
  "name": "موقع الأخبار",
  "url": "https://example.com",
  "selectors": {
    "item": "article, .news-item",
    "title": "h1, h2, .title",
    "link": "a",
    "date": ".date, time"
  },
  "protected": false
}
```

### تعديل فلاتر الأخبار
عدل دالة `filter_news` في `api/app.py`

## 📈 الأداء

- **المعالجة المتوازية**: حتى 6 مصادر في نفس الوقت
- **تحكم في الموارد**: تأخير بين الطلبات لتجنب الحظر
- **ذاكرة التخزين المؤقت**: تجنب تكرار الأخبار

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push للفرع
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## 📞 الدعم

للحصول على الدعم:
- إنشاء Issue في GitHub
- مراجعة ملف السجلات `rasd_media.log`
- تشغيل `python test_system.py` للتشخيص
playwright install
```

## التشغيل
```bash
python main.py
```
