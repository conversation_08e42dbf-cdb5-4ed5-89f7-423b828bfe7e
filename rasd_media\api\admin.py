from flask import Flask, render_template_string, request, redirect, url_for, jsonify
import json
import os
from scraper.scraper import Scraper
from browser_automation.browser_scraper import BrowserScraper

SOURCES_FILE = os.path.join(os.path.dirname(__file__), '..', 'sources.json')

app = Flask(__name__)

# HTML Template (inline for simplicity)
TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <title>إدارة المصادر الإخبارية</title>
    <style>
        body { font-family: Tahoma, Arial; margin: 40px; background: #f9f9f9; }
        table { border-collapse: collapse; width: 100%; background: #fff; }
        th, td { border: 1px solid #ccc; padding: 8px; text-align: right; }
        th { background: #eee; }
        form { margin-bottom: 20px; }
        input, select { padding: 5px; margin: 2px; }
        .actions { white-space: nowrap; }
        .ok { color: green; }
        .fail { color: red; }
    </style>
</head>
<body>
    <h2>إدارة المصادر الإخبارية</h2>
    <form method="post" action="/admin/add">
        <input name="name" placeholder="اسم المصدر" required>
        <input name="url" placeholder="رابط الموقع" required>
        <input name="item" placeholder="CSS: العنصر الرئيسي" required>
        <input name="title" placeholder="CSS: العنوان" required>
        <input name="link" placeholder="CSS: الرابط" required>
        <input name="date" placeholder="CSS: التاريخ (اختياري)">
        <select name="protected">
            <option value="false">عادي</option>
            <option value="true">موقع محمي</option>
        </select>
        <button type="submit">إضافة مصدر</button>
    </form>
    <table>
        <tr><th>الاسم</th><th>الرابط</th><th>العنصر</th><th>العنوان</th><th>الرابط</th><th>التاريخ</th><th>محمي؟</th><th>إجراءات</th></tr>
        {% for s in sources %}
        <tr>
            <form method="post" action="/admin/edit/{{ loop.index0 }}">
            <td><input name="name" value="{{s.name}}" required></td>
            <td><input name="url" value="{{s.url}}" required></td>
            <td><input name="item" value="{{s.selectors.item}}" required></td>
            <td><input name="title" value="{{s.selectors.title}}" required></td>
            <td><input name="link" value="{{s.selectors.link}}" required></td>
            <td><input name="date" value="{{s.selectors.date|default('')}}"></td>
            <td>
                <select name="protected">
                    <option value="false" {% if not s.protected %}selected{% endif %}>عادي</option>
                    <option value="true" {% if s.protected %}selected{% endif %}>محمي</option>
                </select>
            </td>
            <td class="actions">
                <button type="submit">تعديل</button>
                <a href="/admin/delete/{{ loop.index0 }}" onclick="return confirm('حذف المصدر؟')">حذف</a>
                <a href="#" onclick="testSource({{ loop.index0 }});return false;">اختبار</a>
                <span id="result{{ loop.index0 }}"></span>
            </td>
            </form>
        </tr>
        {% endfor %}
    </table>
    <script>
    function testSource(idx) {
        fetch('/admin/test/' + idx).then(r=>r.json()).then(d=>{
            let el = document.getElementById('result'+idx);
            if(d.ok) el.innerHTML = '<span class="ok">✔</span>';
            else el.innerHTML = '<span class="fail">✖ '+(d.error||'فشل')+'</span>';
        });
    }
    </script>
</body>
</html>
'''

def load_sources():
    if not os.path.exists(SOURCES_FILE):
        return []
    with open(SOURCES_FILE, encoding='utf-8') as f:
        return json.load(f)

def save_sources(sources):
    with open(SOURCES_FILE, 'w', encoding='utf-8') as f:
        json.dump(sources, f, ensure_ascii=False, indent=2)

@app.route('/admin', methods=['GET'])
def admin():
    sources = load_sources()
    return render_template_string(TEMPLATE, sources=sources)

@app.route('/admin/add', methods=['POST'])
def add_source():
    sources = load_sources()
    selectors = {
        'item': request.form['item'],
        'title': request.form['title'],
        'link': request.form['link'],
    }
    if request.form.get('date'):
        selectors['date'] = request.form['date']
    sources.append({
        'name': request.form['name'],
        'url': request.form['url'],
        'selectors': selectors,
        'protected': request.form['protected'] == 'true'
    })
    save_sources(sources)
    return redirect(url_for('admin'))

@app.route('/admin/edit/<int:idx>', methods=['POST'])
def edit_source(idx):
    sources = load_sources()
    if 0 <= idx < len(sources):
        selectors = {
            'item': request.form['item'],
            'title': request.form['title'],
            'link': request.form['link'],
        }
        if request.form.get('date'):
            selectors['date'] = request.form['date']
        sources[idx] = {
            'name': request.form['name'],
            'url': request.form['url'],
            'selectors': selectors,
            'protected': request.form['protected'] == 'true'
        }
        save_sources(sources)
    return redirect(url_for('admin'))

@app.route('/admin/delete/<int:idx>')
def delete_source(idx):
    sources = load_sources()
    if 0 <= idx < len(sources):
        sources.pop(idx)
        save_sources(sources)
    return redirect(url_for('admin'))

@app.route('/admin/test/<int:idx>')
def test_source(idx):
    sources = load_sources()
    if 0 <= idx < len(sources):
        src = sources[idx]
        try:
            if src['protected']:
                items = BrowserScraper(src['url'], src['selectors']).scrape()
            else:
                items = Scraper(src['url'], src['selectors']).scrape()
            if items:
                return jsonify({'ok': True, 'count': len(items)})
            else:
                return jsonify({'ok': False, 'error': 'لا توجد أخبار'})
        except Exception as e:
            return jsonify({'ok': False, 'error': str(e)})
    return jsonify({'ok': False, 'error': 'مصدر غير موجود'})

if __name__ == '__main__':
    app.run(debug=True, port=5001)
